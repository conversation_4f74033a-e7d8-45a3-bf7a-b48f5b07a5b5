<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>cha.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{46,47}</string>
                <key>spriteSourceSize</key>
                <string>{46,47}</string>
                <key>textureRect</key>
                <string>{{643,254},{46,47}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>dice.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{76,72}</string>
                <key>spriteSourceSize</key>
                <string>{76,72}</string>
                <key>textureRect</key>
                <string>{{561,451},{76,72}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>gloves.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{68,83}</string>
                <key>spriteSourceSize</key>
                <string>{68,83}</string>
                <key>textureRect</key>
                <string>{{643,1},{68,83}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>next.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{223,80}</string>
                <key>spriteSourceSize</key>
                <string>{223,80}</string>
                <key>textureRect</key>
                <string>{{561,1},{223,80}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>replay.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{223,80}</string>
                <key>spriteSourceSize</key>
                <string>{223,80}</string>
                <key>textureRect</key>
                <string>{{561,226},{223,80}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>star41.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{61,61}</string>
                <key>spriteSourceSize</key>
                <string>{61,61}</string>
                <key>textureRect</key>
                <string>{{643,86},{61,61}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>star42.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{50,50}</string>
                <key>spriteSourceSize</key>
                <string>{50,50}</string>
                <key>textureRect</key>
                <string>{{643,202},{50,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>star43.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{53,51}</string>
                <key>spriteSourceSize</key>
                <string>{53,51}</string>
                <key>textureRect</key>
                <string>{{643,149},{53,51}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>star5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{70,71}</string>
                <key>spriteSourceSize</key>
                <string>{70,71}</string>
                <key>textureRect</key>
                <string>{{639,451},{70,71}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>tooltip.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{558,538}</string>
                <key>spriteSourceSize</key>
                <string>{558,538}</string>
                <key>textureRect</key>
                <string>{{1,1},{558,538}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>tooltip2.png</string>
            <key>size</key>
            <string>{712,540}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:0e57d46dc2d321bc0bc5bbcc134590e6:b9c2cd393177ae7c6dd6c85f9a37ee6f:da51b19783819b5c1db48d0b2bea9376$</string>
            <key>textureFileName</key>
            <string>tooltip2.png</string>
        </dict>
    </dict>
</plist>
