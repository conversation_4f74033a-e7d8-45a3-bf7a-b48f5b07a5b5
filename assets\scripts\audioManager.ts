import { _decorator, Component, Node, AudioSource, AudioClip, CCFloat } from 'cc';
const { ccclass, property } = _decorator;
import { LogManager } from './logManager';

/**
 * 音频管理器 - 稳定版本
 */
@ccclass('AudioManager')
export class AudioManager extends Component {
    
    @property({ type: AudioClip, displayName: "加载音乐" })
    loadingMusic: AudioClip = null!;
    
    @property({ type: AudioClip, displayName: "游戏背景音乐" })
    gameplayBGM: AudioClip = null!;
    
    @property({ type: [AudioClip], displayName: "音效列表" })
    soundEffects: AudioClip[] = [];
    
    @property({ type: CCFloat, range: [0, 1, 0.1], displayName: "背景音乐音量" })
    bgmVolume: number = 0.7;

    @property({ type: CCFloat, range: [0, 1, 0.1], displayName: "音效音量" })
    sfxVolume: number = 0.8;

    @property({ displayName: "启用音频" })
    audioEnabled: boolean = true;
    
    // 音频源组件
    private bgmAudioSource: AudioSource = null!;
    private sfxAudioSource: AudioSource = null!;
    
    // 单例实例
    private static instance: AudioManager = null!;
    
    // 当前状态
    private currentState: string = "none";
    
    onLoad() {
        // 单例模式
        if (AudioManager.instance == null) {
            AudioManager.instance = this;

            try {
                this.initAudioSources();
            } catch (error) {
                LogManager.audioManager.error("❌ [音频系统] 音频源初始化失败", error);
            }
        } else {
            this.node.destroy();
            return;
        }
    }
    
    start() {
        // 开始播放加载音乐
        this.playLoadingMusic();
    }
    
    onDestroy() {
        if (AudioManager.instance === this) {
            AudioManager.instance = null!;
        }
    }
    
    /**
     * 获取单例实例
     */
    public static getInstance(): AudioManager {
        return AudioManager.instance;
    }
    
    /**
     * 初始化音频源
     */
    private initAudioSources() {
        // 创建BGM音频源
        const bgmNode = new Node("BGM_AudioSource");
        bgmNode.parent = this.node;
        this.bgmAudioSource = bgmNode.addComponent(AudioSource);
        this.bgmAudioSource.volume = this.bgmVolume;
        this.bgmAudioSource.loop = true;
        this.bgmAudioSource.playOnAwake = false;
        
        // 创建SFX音频源
        const sfxNode = new Node("SFX_AudioSource");
        sfxNode.parent = this.node;
        this.sfxAudioSource = sfxNode.addComponent(AudioSource);
        this.sfxAudioSource.volume = this.sfxVolume;
        this.sfxAudioSource.loop = false;
        this.sfxAudioSource.playOnAwake = false;
    }
    
    /**
     * 播放加载音乐
     */
    public playLoadingMusic() {
        if (!this.audioEnabled || !this.loadingMusic || !this.bgmAudioSource) {
            return;
        }
        

        this.currentState = "loading";
        this.bgmAudioSource.stop();
        this.bgmAudioSource.clip = this.loadingMusic;
        this.bgmAudioSource.play();
    }
    
    /**
     * 播放游戏背景音乐
     */
    public playGameplayBGM() {
        if (!this.audioEnabled || !this.gameplayBGM || !this.bgmAudioSource) {
            return;
        }
        

        this.currentState = "gameplay";
        this.bgmAudioSource.stop();
        this.bgmAudioSource.clip = this.gameplayBGM;
        this.bgmAudioSource.play();
    }
    
    /**
     * 播放音效
     */
    public playSoundEffect(index: number) {
        if (!this.audioEnabled) {
            LogManager.audioManager.warn("⚠️ [音效播放] 音频已禁用，跳过音效播放");
            return;
        }

        if (!this.sfxAudioSource) {
            LogManager.audioManager.error("❌ [音效播放] 音效音频源不可用");
            return;
        }

        if (index < 0 || index >= this.soundEffects.length) {
            LogManager.audioManager.error(`❌ [音效播放] 音效索引超出范围: ${index}, 可用范围: 0-${this.soundEffects.length - 1}`);
            return;
        }

        const clip = this.soundEffects[index];
        if (clip) {
            try {
                this.sfxAudioSource.playOneShot(clip, this.sfxVolume);
                LogManager.audioManager.log(`🔊 [音效播放] 音效播放成功: 索引${index}, 音量${this.sfxVolume}`);
            } catch (error) {
                LogManager.audioManager.error(`❌ [音效播放] 音效播放失败: 索引${index}`, error);
            }
        } else {
            LogManager.audioManager.error(`❌ [音效播放] 音效资源不存在: 索引${index}`);
        }
    }
    
    /**
     * 播放点击音效
     */
    public playClickSound() {
        this.playSoundEffect(0); // 假设第一个是点击音效
    }
    
    /**
     * 播放成功音效
     */
    public playMatchSound() {
        this.playSoundEffect(1); // 假设第二个是成功音效
    }
    
    /**
     * 播放失败音效
     */
    public playFailSound() {
        this.playSoundEffect(2); // 假设第三个是失败音效
    }
    
    /**
     * 播放胜利音效
     */
    public playWinSound() {
        this.playSoundEffect(3); // 假设第四个是胜利音效
    }
    
    /**
     * 停止背景音乐
     */
    public stopBGM() {
        if (this.bgmAudioSource) {
            try {
                this.bgmAudioSource.stop();
                LogManager.audioManager.log("🔇 [背景音乐] 背景音乐已停止");
            } catch (error) {
                LogManager.audioManager.error("❌ [背景音乐] 停止背景音乐失败", error);
            }
        } else {
            LogManager.audioManager.warn("⚠️ [背景音乐] 背景音乐音频源不可用");
        }
    }
    
    /**
     * 暂停背景音乐
     */
    public pauseBGM() {
        if (this.bgmAudioSource && this.bgmAudioSource.playing) {
            this.bgmAudioSource.pause();

        }
    }
    
    /**
     * 恢复背景音乐
     */
    public resumeBGM() {
        if (this.bgmAudioSource) {
            this.bgmAudioSource.play();

        }
    }
    
    /**
     * 设置BGM音量
     */
    public setBGMVolume(volume: number) {
        this.bgmVolume = Math.max(0, Math.min(1, volume));
        if (this.bgmAudioSource) {
            this.bgmAudioSource.volume = this.bgmVolume;
        }
    }
    
    /**
     * 设置SFX音量
     */
    public setSFXVolume(volume: number) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        if (this.sfxAudioSource) {
            this.sfxAudioSource.volume = this.sfxVolume;
        }
    }
    
    /**
     * 启用/禁用音频
     */
    public setAudioEnabled(enabled: boolean) {
        this.audioEnabled = enabled;
        if (!enabled) {
            this.stopBGM();
        }

    }
    
    /**
     * 游戏开始
     */
    public onGameStart() {

        // 立即停止当前音乐
        this.stopBGM();
        // 立即播放游戏BGM，无延迟
        this.playGameplayBGM();
    }

    /**
     * 游戏开始（带延迟版本，保留兼容性）
     */
    public onGameStartDelayed() {

        setTimeout(() => {
            this.stopBGM();
            this.playGameplayBGM();
        }, 1000);
    }
    
    /**
     * 获取音频状态
     */
    public getAudioInfo(): string {
        return `状态: ${this.currentState}, 音频启用: ${this.audioEnabled}`;
    }
}
